# DateTimePicker Component Documentation

## Overview
The DateTimePicker component is a simple, uncomplicated React component that supports both Hijri and Gregorian calendars with customizable formatting options.

## Features
- ✅ Supports both Hijri and Gregorian calendar systems
- ✅ Customizable date and time formats
- ✅ Built-in time selection with 15-minute intervals
- ✅ Clean, responsive design with Tailwind CSS
- ✅ TypeScript support with proper type definitions
- ✅ Callback function for date changes

## Installation Steps

### 1. Install Required Dependencies
```bash
npm install react-datepicker @types/react-datepicker moment moment-hijri
```

### 2. Component Location
The component is located at: `components/shared/DateTimePicker.tsx`

## Props Interface

```typescript
interface DateTimePickerProps {
  format?: string;           // Date format string (default: 'YYYY-MM-DD HH:mm')
  dateType?: 'hijri' | 'gregorian';  // Calendar type (default: 'gregorian')
  onChange?: (formattedDate: string, rawDate: Date) => void;  // Callback function
  placeholder?: string;      // Placeholder text (default: 'Select date and time')
  className?: string;        // Additional CSS classes
}
```

## Usage Examples

### Basic Gregorian Calendar
```tsx
<DateTimePicker
  dateType="gregorian"
  format="YYYY-MM-DD HH:mm"
  onChange={(formatted, raw) => console.log('Selected:', formatted)}
/>
```

### Basic Hijri Calendar
```tsx
<DateTimePicker
  dateType="hijri"
  format="YYYY-MM-DD HH:mm"
  onChange={(formatted, raw) => console.log('Selected:', formatted)}
/>
```

### Custom Formats
```tsx
// Gregorian with custom format
<DateTimePicker
  dateType="gregorian"
  format="DD/MM/YYYY hh:mm A"
  onChange={(formatted, raw) => console.log('Gregorian:', formatted)}
/>

// Hijri with custom format
<DateTimePicker
  dateType="hijri"
  format="DD/MM/YYYY hh:mm A"
  onChange={(formatted, raw) => console.log('Hijri:', formatted)}
/>
```

## Format Patterns

### Gregorian Formats
- `YYYY-MM-DD HH:mm` → 2024-01-15 14:30
- `DD/MM/YYYY hh:mm A` → 15/01/2024 02:30 PM
- `MMM DD, YYYY HH:mm` → Jan 15, 2024 14:30

### Hijri Formats
When `dateType="hijri"`, the component automatically converts format patterns:
- `YYYY-MM-DD HH:mm` becomes `iYYYY-iMM-iDD HH:mm` → 1445-06-05 14:30
- `DD/MM/YYYY hh:mm A` becomes `iDD/iMM/iYYYY hh:mm A` → 05/06/1445 02:30 PM

## Implementation Details

### Key Functions

1. **handleDateChange**: Processes date selection and formats output
2. **getDisplayFormat**: Converts format patterns for Hijri calendar
3. **Format Conversion**: Automatically replaces Y/M/D with iY/iM/iD for Hijri dates

### Dependencies Used
- `react-datepicker`: Core date picker functionality
- `moment`: Date formatting and manipulation
- `moment-hijri`: Hijri calendar support
- `react-datepicker/dist/react-datepicker.css`: Default styling

## Styling
The component uses Tailwind CSS classes for styling:
- Responsive design with full width
- Focus states with blue ring
- Border and shadow effects
- Customizable through className prop

## Demo Page
The component is demonstrated on the dummy safa page at `/dummy/safa` with examples of:
- Gregorian calendar with default format
- Gregorian calendar with custom format
- Hijri calendar with default format
- Hijri calendar with custom format

## Browser Console Output
When dates are selected, formatted results are logged to the browser console for testing and debugging purposes.
