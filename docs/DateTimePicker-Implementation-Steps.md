# DateTimePicker Implementation Steps

## Step-by-Step Implementation Guide

### Step 1: Install Dependencies
```bash
npm install react-datepicker @types/react-datepicker
```

**What this does:**
- `react-datepicker`: Provides the core date picker functionality
- `@types/react-datepicker`: TypeScript type definitions for better development experience

**Note:** `moment` and `moment-hijri` were already installed in the project.

### Step 2: Create the Component File
**Location:** `components/shared/DateTimePicker.tsx`

**Code Structure:**
```typescript
'use client';

import React, { useState } from "react";
import DatePicker from "react-datepicker";
import moment from "moment";
import momentHijri from "moment-hijri";
import "react-datepicker/dist/react-datepicker.css";
```

**Explanation:**
- `'use client'`: Next.js directive for client-side rendering
- Import necessary libraries for date handling and UI
- CSS import for default date picker styling

### Step 3: Define TypeScript Interface
```typescript
interface DateTimePickerProps {
  format?: string;
  dateType?: "hijri" | "gregorian";
  onChange?: (formattedDate: string, rawDate: Date) => void;
  placeholder?: string;
  className?: string;
}
```

**Props Explanation:**
- `format`: Controls how the date is displayed (e.g., "YYYY-MM-DD HH:mm")
- `dateType`: Switches between Hijri and Gregorian calendars
- `onChange`: Callback function that receives formatted date and raw Date object
- `placeholder`: Text shown when no date is selected
- `className`: Additional CSS classes for customization

### Step 4: Component State Management
```typescript
const [selectedDate, setSelectedDate] = useState<Date | null>(new Date());
```

**Explanation:**
- Uses React's useState hook to manage the selected date
- Initializes with current date (`new Date()`)
- Allows null values for when no date is selected

### Step 5: Date Change Handler
```typescript
const handleDateChange = (date: Date | null) => {
  setSelectedDate(date);
  
  if (date && onChange) {
    let formattedDate: string;
    
    if (dateType === "hijri") {
      const hijriMoment = momentHijri(date);
      formattedDate = hijriMoment.format(
        format.replace(/Y/g, "iY").replace(/M/g, "iM").replace(/D/g, "iD")
      );
    } else {
      formattedDate = moment(date).format(format);
    }
    
    onChange(formattedDate, date);
  }
};
```

**Key Logic:**
1. Updates component state with new date
2. Checks if date exists and onChange callback is provided
3. For Hijri dates: Converts format patterns (Y→iY, M→iM, D→iD)
4. For Gregorian dates: Uses standard moment formatting
5. Calls onChange with both formatted string and raw Date object

### Step 6: Format Display Helper
```typescript
const getDisplayFormat = () => {
  if (dateType === "hijri") {
    return format.replace(/Y/g, "iY").replace(/M/g, "iM").replace(/D/g, "iD");
  }
  return format;
};
```

**Purpose:**
- Converts Gregorian format patterns to Hijri equivalents
- Ensures the date picker displays dates in the correct format

### Step 7: Render the Component
```typescript
return (
  <div className="w-full">
    <DatePicker
      selected={selectedDate}
      onChange={handleDateChange}
      showTimeSelect
      timeFormat="HH:mm"
      timeIntervals={15}
      dateFormat={getDisplayFormat()}
      placeholderText={placeholder}
      className={`w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${className}`}
      wrapperClassName="w-full"
    />
  </div>
);
```

**DatePicker Props Explained:**
- `selected`: Currently selected date
- `onChange`: Handler for date changes
- `showTimeSelect`: Enables time selection
- `timeFormat`: 24-hour time format
- `timeIntervals`: 15-minute intervals for time selection
- `dateFormat`: Display format for the input field
- `placeholderText`: Placeholder when no date selected
- `className`: Tailwind CSS styling for professional appearance
- `wrapperClassName`: Ensures full width wrapper

### Step 8: Update the Demo Page
**Location:** `app/dummy/safa/page.tsx`

**Added Examples:**
1. **Gregorian Default Format**
   ```tsx
   <DateTimePicker
     dateType="gregorian"
     format="YYYY-MM-DD HH:mm"
     onChange={(formatted, raw) => console.log("Gregorian Default:", formatted, raw)}
   />
   ```

2. **Gregorian Custom Format**
   ```tsx
   <DateTimePicker
     dateType="gregorian"
     format="DD/MM/YYYY hh:mm A"
     onChange={(formatted, raw) => console.log("Gregorian Custom:", formatted, raw)}
   />
   ```

3. **Hijri Default Format**
   ```tsx
   <DateTimePicker
     dateType="hijri"
     format="YYYY-MM-DD HH:mm"
     onChange={(formatted, raw) => console.log("Hijri Default:", formatted, raw)}
   />
   ```

4. **Hijri Custom Format**
   ```tsx
   <DateTimePicker
     dateType="hijri"
     format="DD/MM/YYYY hh:mm A"
     onChange={(formatted, raw) => console.log("Hijri Custom:", formatted, raw)}
   />
   ```

### Step 9: Testing
1. **Start Development Server:**
   ```bash
   npm run dev
   ```

2. **Navigate to Demo Page:**
   Open `http://localhost:3000/dummy/safa`

3. **Test Functionality:**
   - Click on each date picker
   - Select different dates and times
   - Check browser console for formatted output
   - Verify Hijri vs Gregorian formatting differences

## Key Features Implemented

### ✅ Simple and Uncomplicated
- Clean, minimal code structure
- Easy to understand and modify
- No complex dependencies or configurations

### ✅ Dual Calendar Support
- Seamless switching between Hijri and Gregorian
- Automatic format conversion for Hijri dates
- Proper moment-hijri integration

### ✅ Flexible Formatting
- Supports various date/time format patterns
- Custom format examples provided
- Real-time format preview

### ✅ Professional Styling
- Tailwind CSS integration
- Responsive design
- Focus states and hover effects
- Consistent with existing UI components

### ✅ TypeScript Support
- Full type safety
- IntelliSense support
- Clear prop definitions
- Error prevention at compile time

## Common Format Patterns

| Pattern | Gregorian Output | Hijri Output |
|---------|------------------|--------------|
| YYYY-MM-DD | 2024-01-15 | 1445-06-05 |
| DD/MM/YYYY | 15/01/2024 | 05/06/1445 |
| MMM DD, YYYY | Jan 15, 2024 | Jumada II 05, 1445 |
| HH:mm | 14:30 | 14:30 |
| hh:mm A | 02:30 PM | 02:30 PM |
