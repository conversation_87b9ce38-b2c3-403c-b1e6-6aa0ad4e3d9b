# DateTimePicker Component Documentation

## Overview

The **DateTimePicker** component is a modern, feature-rich date and time picker built with `react-datetime-picker` library. It supports both **Hijri** and **Gregorian** calendars with customizable formatting, professional styling, and enhanced user experience.

## Features

✨ **Dual Calendar Support**: Hijri and Gregorian calendars  
🎨 **Professional Styling**: Modern gradient design with smooth animations  
📅 **Hijri Month Display**: Shows proper Arabic month names  
⚡ **Real-time Updates**: Instant feedback with formatted date display  
🔧 **Customizable Formats**: Multiple date/time format patterns  
📱 **Responsive Design**: Works seamlessly on all screen sizes  
🌙 **Hijri Date Indicator**: Beautiful amber-themed Hijri date display  

## Installation

The component uses the following dependencies:

```bash
npm install react-datetime-picker react-calendar react-clock
```

## Basic Usage

```tsx
import DateTimePicker from '@/components/shared/DateTimePicker';

// Gregorian Calendar
<DateTimePicker
  dateType="gregorian"
  format="YYYY-MM-DD HH:mm"
  onChange={(formatted, raw) => console.log(formatted, raw)}
/>

// Hijri Calendar
<DateTimePicker
  dateType="hijri"
  format="DD/MM/YYYY hh:mm A"
  onChange={(formatted, raw) => console.log(formatted, raw)}
/>
```

## Props Interface

```typescript
interface DateTimePickerProps {
  format?: string;           // Date format pattern
  dateType?: "hijri" | "gregorian";  // Calendar type
  onChange?: (formattedDate: string, rawDate: Date | null) => void;
  placeholder?: string;      // Input placeholder text
  className?: string;        // Additional CSS classes
}
```

## Supported Format Patterns

| Pattern | Description | Example |
|---------|-------------|---------|
| `YYYY-MM-DD HH:mm` | ISO format with 24h time | `2025-01-15 14:30` |
| `DD/MM/YYYY hh:mm A` | European format with 12h time | `15/01/2025 02:30 PM` |
| `MM/DD/YYYY HH:mm` | US format with 24h time | `01/15/2025 14:30` |
| `YYYY/MM/DD hh:mm A` | Asian format with 12h time | `2025/01/15 02:30 PM` |

## Hijri Calendar Features

### Month Names
The component displays proper Arabic month names:
- محرم (Muharram)
- صفر (Safar)
- ربيع الأول (Rabi' al-awwal)
- ربيع الثاني (Rabi' al-thani)
- جمادى الأولى (Jumada al-awwal)
- جمادى الثانية (Jumada al-thani)
- رجب (Rajab)
- شعبان (Sha'ban)
- رمضان (Ramadan)
- شوال (Shawwal)
- ذو القعدة (Dhu al-Qi'dah)
- ذو الحجة (Dhu al-Hijjah)

### Hijri Date Display
When using Hijri calendar, the component shows an elegant amber-themed display box with:
- 🌙 Crescent moon icon
- Arabic month names
- Hijri year calculation
- Beautiful gradient background

## Styling Features

### Modern Design Elements
- **Gradient Backgrounds**: Subtle gradients for depth
- **Smooth Animations**: Cubic-bezier transitions
- **Hover Effects**: Interactive feedback with transforms
- **Focus States**: Clear focus indicators with shadows
- **Professional Colors**: Blue primary with amber accents

### Calendar Styling
- **Enhanced Navigation**: Gradient header with white text
- **Interactive Tiles**: Hover effects with scaling
- **Today Indicator**: Golden highlight for current date
- **Active Selection**: Blue gradient for selected dates

### Clock Styling
- **3D Effects**: Drop shadows on clock hands
- **Color-coded Hands**: Different colors for hour/minute/second
- **Enhanced Marks**: Varied stroke weights for clarity

## Code Structure

### Component Architecture
```
DateTimePicker/
├── Interface Definition (TypeScript)
├── Hijri Conversion Logic
├── Date Formatting Function
├── React Component
├── Hijri Display Component
└── Styled JSX Styling
```

### Key Functions

#### `gregorianToHijri(date: Date)`
Converts Gregorian dates to Hijri using lunar year approximation:
- Uses 354.37 days average for Hijri year
- Calculates from Hijri epoch (622 CE)
- Returns year, month, day, and Arabic month name

#### `formatDate(date: Date, format: string, isHijri: boolean)`
Formats dates according to specified patterns:
- Supports multiple format patterns
- Handles both calendar systems
- Returns formatted string

## Implementation Examples

### Example 1: Basic Gregorian Picker
```tsx
<DateTimePicker
  dateType="gregorian"
  format="YYYY-MM-DD HH:mm"
  onChange={(formatted, raw) => {
    console.log('Selected:', formatted);
    // Handle date selection
  }}
/>
```

### Example 2: Hijri Calendar with Custom Format
```tsx
<DateTimePicker
  dateType="hijri"
  format="DD/MM/YYYY hh:mm A"
  onChange={(formatted, raw) => {
    console.log('Hijri Date:', formatted);
    // Handle Hijri date selection
  }}
  className="custom-picker"
/>
```

### Example 3: Event Handler Integration
```tsx
const [selectedDate, setSelectedDate] = useState('');

<DateTimePicker
  dateType="gregorian"
  format="MM/DD/YYYY HH:mm"
  onChange={(formatted, raw) => {
    setSelectedDate(formatted);
    // Additional logic here
  }}
/>
```

## Browser Compatibility

- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

## Performance Notes

- Component uses React hooks for state management
- Minimal re-renders with optimized change handlers
- CSS-in-JS for scoped styling
- Lightweight Hijri conversion algorithm

## Troubleshooting

### Common Issues

1. **Styling not applied**: Ensure CSS imports are included
2. **Hijri dates incorrect**: Algorithm is approximation, not astronomically precise
3. **Format not working**: Check format pattern syntax
4. **Calendar not opening**: Verify all dependencies are installed

### Debug Tips

- Check browser console for errors
- Verify prop types match interface
- Test with different format patterns
- Ensure proper import statements

## Future Enhancements

- 🔮 Astronomical Hijri calculations
- 🌍 Multiple language support
- 📊 Date range selection
- 🎯 Custom validation rules
- 📱 Mobile-optimized gestures

---

**Built with ❤️ using React, TypeScript, and react-datetime-picker**
