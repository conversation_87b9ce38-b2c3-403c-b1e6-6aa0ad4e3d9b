"use client";

import SummaryCard from "../../../components/SummaryCard";
import { useLanguage } from "../../../contexts/LanguageContext";
import DashboardSummaryCards from "../../../components/DashboardSummaryCards";
import DateTimePicker from "../../../components/shared/DateTimePicker";

export default function DummyCardsPage() {
  const { t, dir } = useLanguage();
  const cardSize = "sm";
  const showTrends = false;
  const layout = "auto";

  const dummyCards = [
    {
      id: "inactive-trips-count",
      titleKey: "dashboard.card.inactiveTrips",
      value: 1551,
      icon: "bell-off" as const,
      color: "gray" as const,
      trend: { value: 0, direction: "stable" as const },
    },
    {
      id: "resolved-trips-count",
      titleKey: "dashboard.card.resolvedToday",
      value: 1562,
      icon: "check" as const,
      color: "green" as const,
      trend: { value: 8.7, direction: "up" as const },
    },
    {
      id: "critical-alerts-count",
      titleKey: "dashboard.card.criticalAlerts",
      value: 0,
      icon: "prohibition" as const,
      color: "red" as const,
      trend: { value: 12.3, direction: "down" as const },
    },
    {
      id: "pending-reviews-count",
      titleKey: "dashboard.card.pendingAlerts",
      value: 847,
      icon: "bell-gray" as const,
      color: "gray" as const,
      trend: { value: 3.4, direction: "up" as const },
    },
    {
      id: "completed-today-count",
      titleKey: "dashboard.card.totalAlerts",
      value: 2156,
      icon: "check" as const,
      color: "blue" as const,
      trend: { value: 15.2, direction: "up" as const },
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50" dir={dir}>
      <div className="p-6">
        {/* Dashboard Summary Cards Section */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            {t("dummy.section.dashboardCards")}
          </h2>
          <div className="bg-white rounded-lg shadow-sm p-4">
            <DashboardSummaryCards
              cards={dummyCards}
              layout={layout}
              showTrends={showTrends}
              cardSize={cardSize}
              className="mb-2"
            />
          </div>
        </div>
        <div className="mb-4">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            {t("dummy.section.individualCards")}
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4">
            {dummyCards.slice(0, 5).map((card) => (
              <SummaryCard
                key={`individual-${card.id}`}
                id={card.id}
                titleKey={card.titleKey}
                value={card.value}
                icon={card.icon}
                color={card.color}
                size={cardSize}
                showTrends={showTrends}
                trend={card.trend}
                className="w-full"
              />
            ))}
          </div>
        </div>

        {/* Date Time Picker Section */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Date Time Picker Examples
          </h2>

          {/* Gregorian Date Time Picker */}
          <div className="bg-white rounded-lg shadow-sm p-6 mb-4">
            <h3 className="text-lg font-medium text-gray-800 mb-3">
              Gregorian Calendar
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Default Format (YYYY-MM-DD HH:mm)
                </label>
                <DateTimePicker
                  dateType="gregorian"
                  format="YYYY-MM-DD HH:mm"
                  onChange={(formatted, raw) =>
                    console.log("Gregorian Default:", formatted, raw)
                  }
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Custom Format (DD/MM/YYYY hh:mm A)
                </label>
                <DateTimePicker
                  dateType="gregorian"
                  format="DD/MM/YYYY hh:mm A"
                  onChange={(formatted, raw) =>
                    console.log("Gregorian Custom:", formatted, raw)
                  }
                />
              </div>
            </div>
          </div>

          {/* Hijri Date Time Picker */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium text-gray-800 mb-3">
              Hijri Calendar
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Default Hijri Format (iYYYY-iMM-iDD HH:mm)
                </label>
                <DateTimePicker
                  dateType="hijri"
                  format="YYYY-MM-DD HH:mm"
                  onChange={(formatted, raw) =>
                    console.log("Hijri Default:", formatted, raw)
                  }
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Custom Hijri Format (iDD/iMM/iYYYY hh:mm A)
                </label>
                <DateTimePicker
                  dateType="hijri"
                  format="DD/MM/YYYY hh:mm A"
                  onChange={(formatted, raw) =>
                    console.log("Hijri Custom:", formatted, raw)
                  }
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
