"use client";

import React, { useState } from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

// Simple Hijri date conversion (approximation)
const gregorianToHijri = (date: Date) => {
  const gregorianYear = date.getFullYear();
  const gregorianMonth = date.getMonth() + 1;
  const gregorianDay = date.getDate();

  // Simple approximation: Hijri year is roughly Gregorian year - 579
  // This is a basic conversion and not astronomically accurate
  const hijriYear = gregorianYear - 579;
  const hijriMonth = gregorianMonth;
  const hijriDay = gregorianDay;

  return { year: hijriYear, month: hijriMonth, day: hijriDay };
};

// Format date without moment.js
const formatDate = (date: Date, format: string, isHijri: boolean = false) => {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hours = date.getHours();
  const minutes = date.getMinutes();

  let formattedDate = format;

  if (isHijri) {
    const hijriDate = gregorianToHijri(date);
    formattedDate = formattedDate
      .replace(/YYYY/g, hijriDate.year.toString().padStart(4, "0"))
      .replace(/YY/g, hijriDate.year.toString().slice(-2))
      .replace(/MM/g, hijriDate.month.toString().padStart(2, "0"))
      .replace(/DD/g, hijriDate.day.toString().padStart(2, "0"));
  } else {
    formattedDate = formattedDate
      .replace(/YYYY/g, year.toString())
      .replace(/YY/g, year.toString().slice(-2))
      .replace(/MM/g, month.toString().padStart(2, "0"))
      .replace(/DD/g, day.toString().padStart(2, "0"));
  }

  // Time formatting
  formattedDate = formattedDate
    .replace(/HH/g, hours.toString().padStart(2, "0"))
    .replace(/hh/g, (hours % 12 || 12).toString().padStart(2, "0"))
    .replace(/mm/g, minutes.toString().padStart(2, "0"))
    .replace(/A/g, hours >= 12 ? "PM" : "AM");

  return formattedDate;
};

interface DateTimePickerProps {
  format?: string;
  dateType?: "hijri" | "gregorian";
  onChange?: (formattedDate: string, rawDate: Date) => void;
  placeholder?: string;
  className?: string;
}

const DateTimePicker: React.FC<DateTimePickerProps> = ({
  format = "YYYY-MM-DD HH:mm",
  dateType = "gregorian",
  onChange,
  placeholder = "Select date and time",
  className = "",
}) => {
  const [selectedDate, setSelectedDate] = useState<Date | null>(new Date());

  const handleDateChange = (date: Date | null) => {
    setSelectedDate(date);

    if (date && onChange) {
      const formattedDate = formatDate(date, format, dateType === "hijri");
      onChange(formattedDate, date);
    }
  };

  const getDisplayFormat = () => {
    // Convert to date-fns format for react-datepicker display
    const displayFormat = format
      .replace(/YYYY/g, "yyyy")
      .replace(/YY/g, "yy")
      .replace(/MM/g, "MM")
      .replace(/DD/g, "dd")
      .replace(/HH/g, "HH")
      .replace(/mm/g, "mm")
      .replace(/hh/g, "hh")
      .replace(/A/g, "aa"); // AM/PM format

    return displayFormat;
  };

  return (
    <div className="w-full">
      <DatePicker
        selected={selectedDate}
        onChange={handleDateChange}
        showTimeSelect
        timeFormat="HH:mm"
        timeIntervals={15}
        dateFormat={getDisplayFormat()}
        placeholderText={placeholder}
        className={`w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${className}`}
        wrapperClassName="w-full"
      />
    </div>
  );
};

export default DateTimePicker;
