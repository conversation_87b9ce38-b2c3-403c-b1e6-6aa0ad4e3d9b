"use client";

import React, { useState } from "react";
import DateTimePicker from "react-datetime-picker";
import "react-datetime-picker/dist/DateTimePicker.css";
import "react-calendar/dist/Calendar.css";
import "react-clock/dist/Clock.css";

// Hijri month names
const hijriMonths = [
  "محرم",
  "صفر",
  "ربيع الأول",
  "ربيع الثاني",
  "جمادى الأولى",
  "جمادى الثانية",
  "رجب",
  "شعبان",
  "رمضان",
  "شوال",
  "ذو القعدة",
  "ذو الحجة",
];

// Enhanced Hijri date conversion (approximation)
const gregorianToHijri = (date: Date) => {
  const gregorianYear = date.getFullYear();
  const gregorianMonth = date.getMonth() + 1;
  const gregorianDay = date.getDate();

  // More accurate approximation using lunar year calculation
  // Average Hijri year is about 354.37 days vs Gregorian 365.25 days
  const hijriEpoch = new Date(622, 6, 16); // July 16, 622 CE (approximate)
  const daysSinceEpoch = Math.floor(
    (date.getTime() - hijriEpoch.getTime()) / (1000 * 60 * 60 * 24)
  );
  const hijriYear = Math.floor(daysSinceEpoch / 354.37) + 1;

  // Simple month approximation (can be improved with proper lunar calculations)
  const hijriMonth =
    ((gregorianMonth - 1 + Math.floor(hijriYear / 33)) % 12) + 1;
  const hijriDay = gregorianDay;

  return {
    year: hijriYear,
    month: hijriMonth,
    day: hijriDay,
    monthName: hijriMonths[hijriMonth - 1],
  };
};

// Format date without moment.js
const formatDate = (date: Date, format: string, isHijri: boolean = false) => {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hours = date.getHours();
  const minutes = date.getMinutes();

  let formattedDate = format;

  if (isHijri) {
    const hijriDate = gregorianToHijri(date);
    formattedDate = formattedDate
      .replace(/YYYY/g, hijriDate.year.toString().padStart(4, "0"))
      .replace(/YY/g, hijriDate.year.toString().slice(-2))
      .replace(/MM/g, hijriDate.month.toString().padStart(2, "0"))
      .replace(/DD/g, hijriDate.day.toString().padStart(2, "0"));
  } else {
    formattedDate = formattedDate
      .replace(/YYYY/g, year.toString())
      .replace(/YY/g, year.toString().slice(-2))
      .replace(/MM/g, month.toString().padStart(2, "0"))
      .replace(/DD/g, day.toString().padStart(2, "0"));
  }

  // Time formatting
  formattedDate = formattedDate
    .replace(/HH/g, hours.toString().padStart(2, "0"))
    .replace(/hh/g, (hours % 12 || 12).toString().padStart(2, "0"))
    .replace(/mm/g, minutes.toString().padStart(2, "0"))
    .replace(/A/g, hours >= 12 ? "PM" : "AM");

  return formattedDate;
};

interface DateTimePickerProps {
  format?: string;
  dateType?: "hijri" | "gregorian";
  onChange?: (formattedDate: string, rawDate: Date | null) => void;
  placeholder?: string;
  className?: string;
}

const CustomDateTimePicker: React.FC<DateTimePickerProps> = ({
  format = "YYYY-MM-DD HH:mm",
  dateType = "gregorian",
  onChange,
  placeholder = "Select date and time",
  className = "",
}) => {
  // Always default to today's date and current time for both calendars
  const [selectedDate, setSelectedDate] = useState<Date | null>(new Date());

  const handleDateChange = (date: Date | null) => {
    setSelectedDate(date);

    if (onChange) {
      if (date) {
        const formattedDate = formatDate(date, format, dateType === "hijri");
        onChange(formattedDate, date);
      } else {
        onChange("", null);
      }
    }
  };

  return (
    <div className="w-full">
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          {dateType === "hijri" ? "Hijri Date & Time" : "Date & Time"}
        </label>
        <DateTimePicker
          onChange={handleDateChange}
          value={selectedDate}
          className={`custom-datetime-picker ${
            dateType === "hijri" ? "hijri-calendar" : ""
          } ${className}`}
          format="y-MM-dd h:mm:ss a"
          clearIcon={null}
          locale={dateType === "hijri" ? "ar-SA" : "en-US"}
          calendarIcon={
            <svg
              className="w-5 h-5 text-gray-500 hover:text-blue-600 transition-colors"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
          }
        />

        {/* Hijri Date Display */}
        {dateType === "hijri" && selectedDate && (
          <div className="mt-3 p-3 bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <svg
                className="w-4 h-4 text-amber-600"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
              </svg>
              <span className="text-sm font-medium text-amber-800">
                Hijri Date:
              </span>
              <span
                className="text-sm text-amber-700 font-semibold"
                style={{ fontFamily: "Arial, sans-serif" }}
              >
                {(() => {
                  const hijriDate = gregorianToHijri(selectedDate);
                  return `${hijriDate.day} ${hijriDate.monthName} ${hijriDate.year}`;
                })()}
              </span>
            </div>
          </div>
        )}
      </div>

      <style jsx global>{`
        .custom-datetime-picker {
          width: 100% !important;
        }

        .custom-datetime-picker .react-datetime-picker__wrapper {
          border: 2px solid #e5e7eb !important;
          border-radius: 12px !important;
          padding: 14px 18px !important;
          background: linear-gradient(
            135deg,
            #ffffff 0%,
            #f8fafc 100%
          ) !important;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
            0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
          position: relative !important;
        }

        .custom-datetime-picker .react-datetime-picker__wrapper:hover {
          border-color: #3b82f6 !important;
          box-shadow: 0 8px 25px -5px rgba(59, 130, 246, 0.1),
            0 4px 6px -2px rgba(59, 130, 246, 0.05) !important;
          transform: translateY(-1px) !important;
        }

        .custom-datetime-picker .react-datetime-picker__wrapper:focus-within {
          border-color: #2563eb !important;
          box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.15),
            0 8px 25px -5px rgba(59, 130, 246, 0.2) !important;
          transform: translateY(-1px) !important;
        }

        .custom-datetime-picker .react-datetime-picker__inputGroup {
          font-size: 14px !important;
          color: #374151 !important;
        }

        .custom-datetime-picker .react-datetime-picker__inputGroup__input {
          color: #374151 !important;
          font-weight: 500 !important;
        }

        .custom-datetime-picker .react-datetime-picker__button {
          color: #6b7280 !important;
          padding: 4px !important;
        }

        .custom-datetime-picker .react-datetime-picker__button:hover {
          background-color: #f3f4f6 !important;
          border-radius: 4px !important;
        }

        .react-calendar {
          border: none !important;
          box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15),
            0 10px 20px -5px rgba(0, 0, 0, 0.1) !important;
          border-radius: 16px !important;
          font-family: inherit !important;
          overflow: hidden !important;
          background: linear-gradient(
            135deg,
            #ffffff 0%,
            #f8fafc 100%
          ) !important;
        }

        .react-calendar__navigation {
          background: linear-gradient(
            135deg,
            #3b82f6 0%,
            #2563eb 100%
          ) !important;
          border: none !important;
          padding: 16px !important;
          margin-bottom: 0 !important;
        }

        .react-calendar__navigation button {
          color: white !important;
          font-weight: 600 !important;
          border-radius: 8px !important;
          transition: all 0.2s ease !important;
        }

        .react-calendar__navigation button:hover {
          background-color: rgba(255, 255, 255, 0.2) !important;
        }

        .react-calendar__month-view__weekdays {
          background-color: #f1f5f9 !important;
          padding: 12px 0 !important;
        }

        .react-calendar__month-view__weekdays__weekday {
          color: #64748b !important;
          font-weight: 600 !important;
          font-size: 12px !important;
          text-transform: uppercase !important;
        }

        .react-calendar__tile {
          border-radius: 10px !important;
          transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
          margin: 2px !important;
          font-weight: 500 !important;
          position: relative !important;
        }

        .react-calendar__tile:hover {
          background: linear-gradient(
            135deg,
            #dbeafe 0%,
            #bfdbfe 100%
          ) !important;
          color: #1d4ed8 !important;
          transform: scale(1.05) !important;
          box-shadow: 0 4px 8px rgba(59, 130, 246, 0.2) !important;
        }

        .react-calendar__tile--active {
          background: linear-gradient(
            135deg,
            #3b82f6 0%,
            #2563eb 100%
          ) !important;
          color: white !important;
          transform: scale(1.05) !important;
          box-shadow: 0 6px 12px rgba(59, 130, 246, 0.3) !important;
        }

        .react-calendar__tile--now {
          background: linear-gradient(
            135deg,
            #fbbf24 0%,
            #f59e0b 100%
          ) !important;
          color: white !important;
          font-weight: 700 !important;
          box-shadow: 0 4px 8px rgba(251, 191, 36, 0.3) !important;
        }

        .react-calendar__tile--now:hover {
          background: linear-gradient(
            135deg,
            #f59e0b 0%,
            #d97706 100%
          ) !important;
        }

        /* Hijri calendar specific styling */
        .hijri-calendar .react-calendar__navigation__label {
          font-family: "Arial", sans-serif !important;
        }

        .react-clock {
          border: none !important;
          box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15),
            0 10px 20px -5px rgba(0, 0, 0, 0.1) !important;
          border-radius: 16px !important;
          background: linear-gradient(
            135deg,
            #ffffff 0%,
            #f8fafc 100%
          ) !important;
          padding: 20px !important;
        }

        .react-clock__face {
          border: 3px solid #e2e8f0 !important;
          background: radial-gradient(
            circle,
            #ffffff 0%,
            #f8fafc 100%
          ) !important;
          box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06) !important;
        }

        .react-clock__hand {
          stroke: #3b82f6 !important;
          stroke-width: 3 !important;
          filter: drop-shadow(0 2px 4px rgba(59, 130, 246, 0.3)) !important;
        }

        .react-clock__hand--hour {
          stroke: #1e40af !important;
          stroke-width: 4 !important;
        }

        .react-clock__hand--minute {
          stroke: #3b82f6 !important;
          stroke-width: 3 !important;
        }

        .react-clock__hand--second {
          stroke: #ef4444 !important;
          stroke-width: 2 !important;
        }

        .react-clock__mark {
          stroke: #64748b !important;
          stroke-width: 2 !important;
        }

        .react-clock__mark--hour {
          stroke: #374151 !important;
          stroke-width: 3 !important;
        }

        .react-clock__mark--minute {
          stroke: #9ca3af !important;
          stroke-width: 1 !important;
        }
      `}</style>
    </div>
  );
};

export default CustomDateTimePicker;
