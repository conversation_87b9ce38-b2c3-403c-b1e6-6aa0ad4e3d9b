"use client";

import React, { useState } from "react";
import DatePicker from "react-datepicker";
import moment from "moment";
import momentHijri from "moment-hijri";
import "react-datepicker/dist/react-datepicker.css";

interface DateTimePickerProps {
  format?: string;
  dateType?: "hijri" | "gregorian";
  onChange?: (formattedDate: string, rawDate: Date) => void;
  placeholder?: string;
  className?: string;
}

const DateTimePicker: React.FC<DateTimePickerProps> = ({
  format = "YYYY-MM-DD HH:mm",
  dateType = "gregorian",
  onChange,
  placeholder = "Select date and time",
  className = "",
}) => {
  const [selectedDate, setSelectedDate] = useState<Date | null>(new Date());

  const handleDateChange = (date: Date | null) => {
    setSelectedDate(date);

    if (date && onChange) {
      let formattedDate: string;

      if (dateType === "hijri") {
        // Convert to Hijri format
        const hijriMoment = momentHijri(date);
        const hijriFormat = format
          .replace(/YYYY/g, "iYYYY")
          .replace(/YY/g, "iYY")
          .replace(/MM/g, "iMM")
          .replace(/DD/g, "iDD");
        formattedDate = hijriMoment.format(hijriFormat);
      } else {
        // Gregorian format
        formattedDate = moment(date).format(format);
      }

      onChange(formattedDate, date);
    }
  };

  const getDisplayFormat = () => {
    let displayFormat = format;

    // Convert moment.js format to date-fns format for react-datepicker
    displayFormat = displayFormat
      .replace(/YYYY/g, "yyyy")
      .replace(/YY/g, "yy")
      .replace(/MM/g, "MM")
      .replace(/DD/g, "dd")
      .replace(/HH/g, "HH")
      .replace(/mm/g, "mm")
      .replace(/hh/g, "hh")
      .replace(/A/g, "aa"); // AM/PM format

    if (dateType === "hijri") {
      displayFormat = displayFormat
        .replace(/yyyy/g, "iyyyy")
        .replace(/yy/g, "iyy")
        .replace(/MM/g, "iMM")
        .replace(/dd/g, "idd");
    }

    return displayFormat;
  };

  return (
    <div className="w-full">
      <DatePicker
        selected={selectedDate}
        onChange={handleDateChange}
        showTimeSelect
        timeFormat="HH:mm"
        timeIntervals={15}
        dateFormat={getDisplayFormat()}
        placeholderText={placeholder}
        className={`w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${className}`}
        wrapperClassName="w-full"
      />
    </div>
  );
};

export default DateTimePicker;
