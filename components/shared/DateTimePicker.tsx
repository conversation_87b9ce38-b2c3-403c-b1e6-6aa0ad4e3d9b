"use client";

import React, { useState } from "react";
import DateTimePicker from "react-datetime-picker";
import "react-datetime-picker/dist/DateTimePicker.css";
import "react-calendar/dist/Calendar.css";
import "react-clock/dist/Clock.css";

// Simple Hijri date conversion (approximation)
const gregorianToHijri = (date: Date) => {
  const gregorianYear = date.getFullYear();
  const gregorianMonth = date.getMonth() + 1;
  const gregorianDay = date.getDate();

  // Simple approximation: Hijri year is roughly Gregorian year - 579
  // This is a basic conversion and not astronomically accurate
  const hijriYear = gregorianYear - 579;
  const hijriMonth = gregorianMonth;
  const hijriDay = gregorianDay;

  return { year: hijriYear, month: hijriMonth, day: hijriDay };
};

// Format date without moment.js
const formatDate = (date: Date, format: string, isHijri: boolean = false) => {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hours = date.getHours();
  const minutes = date.getMinutes();

  let formattedDate = format;

  if (isHijri) {
    const hijriDate = gregorianToHijri(date);
    formattedDate = formattedDate
      .replace(/YYYY/g, hijriDate.year.toString().padStart(4, "0"))
      .replace(/YY/g, hijriDate.year.toString().slice(-2))
      .replace(/MM/g, hijriDate.month.toString().padStart(2, "0"))
      .replace(/DD/g, hijriDate.day.toString().padStart(2, "0"));
  } else {
    formattedDate = formattedDate
      .replace(/YYYY/g, year.toString())
      .replace(/YY/g, year.toString().slice(-2))
      .replace(/MM/g, month.toString().padStart(2, "0"))
      .replace(/DD/g, day.toString().padStart(2, "0"));
  }

  // Time formatting
  formattedDate = formattedDate
    .replace(/HH/g, hours.toString().padStart(2, "0"))
    .replace(/hh/g, (hours % 12 || 12).toString().padStart(2, "0"))
    .replace(/mm/g, minutes.toString().padStart(2, "0"))
    .replace(/A/g, hours >= 12 ? "PM" : "AM");

  return formattedDate;
};

interface DateTimePickerProps {
  format?: string;
  dateType?: "hijri" | "gregorian";
  onChange?: (formattedDate: string, rawDate: Date | null) => void;
  placeholder?: string;
  className?: string;
}

const CustomDateTimePicker: React.FC<DateTimePickerProps> = ({
  format = "YYYY-MM-DD HH:mm",
  dateType = "gregorian",
  onChange,
  placeholder = "Select date and time",
  className = "",
}) => {
  // Always default to today's date and current time for both calendars
  const [selectedDate, setSelectedDate] = useState<Date | null>(new Date());

  const handleDateChange = (date: Date | null) => {
    setSelectedDate(date);

    if (onChange) {
      if (date) {
        const formattedDate = formatDate(date, format, dateType === "hijri");
        onChange(formattedDate, date);
      } else {
        onChange("", null);
      }
    }
  };

  return (
    <div className="w-full">
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          {dateType === "hijri" ? "Hijri Date & Time" : "Date & Time"}
        </label>
        <DateTimePicker
          onChange={handleDateChange}
          value={selectedDate}
          className={`custom-datetime-picker ${className}`}
          format="y-MM-dd h:mm:ss a"
          clearIcon={null}
          calendarIcon={
            <svg
              className="w-5 h-5 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
          }
        />
      </div>

      <style jsx global>{`
        .custom-datetime-picker {
          width: 100% !important;
        }

        .custom-datetime-picker .react-datetime-picker__wrapper {
          border: 2px solid #e5e7eb !important;
          border-radius: 8px !important;
          padding: 12px 16px !important;
          background: white !important;
          transition: all 0.2s ease !important;
          box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
        }

        .custom-datetime-picker .react-datetime-picker__wrapper:hover {
          border-color: #d1d5db !important;
        }

        .custom-datetime-picker .react-datetime-picker__wrapper:focus-within {
          border-color: #3b82f6 !important;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
        }

        .custom-datetime-picker .react-datetime-picker__inputGroup {
          font-size: 14px !important;
          color: #374151 !important;
        }

        .custom-datetime-picker .react-datetime-picker__inputGroup__input {
          color: #374151 !important;
          font-weight: 500 !important;
        }

        .custom-datetime-picker .react-datetime-picker__button {
          color: #6b7280 !important;
          padding: 4px !important;
        }

        .custom-datetime-picker .react-datetime-picker__button:hover {
          background-color: #f3f4f6 !important;
          border-radius: 4px !important;
        }

        .react-calendar {
          border: none !important;
          box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
            0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
          border-radius: 12px !important;
          font-family: inherit !important;
        }

        .react-calendar__tile {
          border-radius: 6px !important;
          transition: all 0.15s ease !important;
        }

        .react-calendar__tile:hover {
          background-color: #dbeafe !important;
          color: #1d4ed8 !important;
        }

        .react-calendar__tile--active {
          background-color: #3b82f6 !important;
          color: white !important;
        }

        .react-calendar__tile--now {
          background-color: #fef3c7 !important;
          color: #92400e !important;
          font-weight: 600 !important;
        }

        .react-clock {
          border: none !important;
          box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1) !important;
          border-radius: 12px !important;
        }

        .react-clock__face {
          border: 2px solid #f3f4f6 !important;
        }

        .react-clock__hand {
          stroke: #3b82f6 !important;
        }

        .react-clock__mark {
          stroke: #6b7280 !important;
        }
      `}</style>
    </div>
  );
};

export default CustomDateTimePicker;
