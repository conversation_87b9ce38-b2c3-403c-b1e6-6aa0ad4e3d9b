"use client";

import React, { useState } from "react";
import DatePicker from "react-datepicker";
import moment from "moment";
import momentHijri from "moment-hijri";
import "react-datepicker/dist/react-datepicker.css";

interface DateTimePickerProps {
  format?: string;
  dateType?: "hijri" | "gregorian";
  onChange?: (formattedDate: string, rawDate: Date) => void;
  placeholder?: string;
  className?: string;
}

const DateTimePicker: React.FC<DateTimePickerProps> = ({
  format = "YYYY-MM-DD HH:mm",
  dateType = "gregorian",
  onChange,
  placeholder = "Select date and time",
  className = "",
}) => {
  const [selectedDate, setSelectedDate] = useState<Date | null>(new Date());

  const handleDateChange = (date: Date | null) => {
    setSelectedDate(date);

    if (date && onChange) {
      let formattedDate: string;

      if (dateType === "hijri") {
        // Convert to Hijri format
        const hijriMoment = momentHijri(date);
        formattedDate = hijriMoment.format(
          format.replace(/Y/g, "iY").replace(/M/g, "iM").replace(/D/g, "iD")
        );
      } else {
        // Gregorian format
        formattedDate = moment(date).format(format);
      }

      onChange(formattedDate, date);
    }
  };

  const getDisplayFormat = () => {
    if (dateType === "hijri") {
      return format.replace(/Y/g, "iY").replace(/M/g, "iM").replace(/D/g, "iD");
    }
    return format;
  };

  return (
    <div className="w-full">
      <DatePicker
        selected={selectedDate}
        onChange={handleDateChange}
        showTimeSelect
        timeFormat="HH:mm"
        timeIntervals={15}
        dateFormat={getDisplayFormat()}
        placeholderText={placeholder}
        className={`w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${className}`}
        wrapperClassName="w-full"
      />
    </div>
  );
};

export default DateTimePicker;
