// components/HijriGregorianDateTimePicker.tsx
'use client';

import React, { useState } from 'react';
import moment from 'moment-hijri';
import DatePicker from 'react-datepicker-hijri';
import 'react-datepicker-hijri/dist/react-datepicker-hijri.css';

type Props = {
  format?: string;
  dateType?: 'hijri' | 'gregorian';
  onChange?: (formatted: string) => void;
};

const HijriGregorianDateTimePicker: React.FC<Props> = ({
  format = 'YYYY-MM-DD HH:mm',
  dateType = 'gregorian',
  onChange,
}) => {
  const [selectedDate, setSelectedDate] = useState<Date | null>(new Date());

  const handleChange = (date: Date) => {
    setSelectedDate(date);

    const formatted =
      dateType === 'hijri'
        ? moment(date).format(format.replace(/Y/g, 'iY').replace(/M/g, 'iM').replace(/D/g, 'iD'))
        : moment(date).format(format);

    onChange?.(formatted);
  };

  return (
    <DatePicker
      selected={selectedDate}
      onChange={handleChange}
      showTimeSelect
      timeFormat="HH:mm"
      timeIntervals={15}
      dateFormat={format}
      calendar={dateType}
      className="border p-2 rounded"
    />
  );
};

export default HijriGregorianDateTimePicker;
